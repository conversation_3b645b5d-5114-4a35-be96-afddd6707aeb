export interface HelpContent {
  greeting: string;
  description: string;
  helpTopics: HelpTopic[];
}
export interface DetailTopicItem {
  name: string;
  description: string;
}
export interface HelpTopicItem {
  title: string;
  items: DetailTopicItem[];
}

export interface HelpTopic {
  title: string;
  items: HelpTopicItem[];
}

type Environment = 'uat' | 'pro';
export interface AppKeys {
  [key: string]: {
    [env in Environment]: string; // 每个环境对应一个字符串
  };
}