export type MessageType = 'question' | 'answer' | 'system';
export type CardType = 'text' | 'help' | 'table' | 'product' | 'waiting' | 'file' | 'error' | 'iframe';
export type MessageStatus = 'waiting' | 'pending' | 'success' | 'error';

export interface EventMessge {
  event: 'agent_message' | 'agent_thought';
  answer: string;
  conversation_id: string;
}

export const baseAppKey = 'app-5XErQWi21l0RWaWSc66ELOiv';
export const baseAppKeyName = '通用查询';

export const DEEPSEEK_APP_KEY_NAME = '深度思考';

export const MESSAGE = {
  AGENT_ERROR: '抱歉，我暂未找到答案，请您重新提出更加详细和明确的问题试试。',
  SYSTEM_ERROR: '系统错误，请稍后再试~',
}
