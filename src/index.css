:root {
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  overflow-y: hidden;
  min-width: 100vw;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

.sender-content .ant-sender-content{
  height: 160px;
  flex-direction: column;
}
.sender-content .ant-sender-content textarea{
  height: 100% !important;
}
.ant-typography p{
  margin: 0;
}

.question-bubble.ant-bubble .ant-bubble-content-filled {
  background: linear-gradient(90deg, #DAD3FA, #D4E7FD);
  border-radius: 21px 0px 21px 21px;
}
.ant-bubble {
  flex-direction: column;
  align-items: flex-start;
}
.ant-bubble .ant-bubble-avatar  {
  margin-bottom: 10px;
}
.answer-bubble.ant-bubble .ant-bubble-content-filled {
  background: #FFFFFF;
  border-radius: 0px 20px 20px 20px;
}
.multi-line-2 {
  width: 100%;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.markdown-body {
  background-color: transparent !important;
  font-size: 14px !important;
}