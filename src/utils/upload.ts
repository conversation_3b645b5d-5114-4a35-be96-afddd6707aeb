import OSS from 'ali-oss';
import { UploadFile } from 'antd';

const isPro = /boss.zkh360.com/.test(location.href);

const region = 'oss-cn-beijing';
const postfix = isPro ? '' : '-uat';
const bucket = `zkh360-boss${postfix}`;
// const fileDomain = `https://files${postfix}.zkh360.com`;
const fileDomain = `https://zkh360-boss${postfix}.oss-cn-beijing.aliyuncs.com`;

// 阿里云oss client
let client: OSS | null = null;
export async function getClient() {
  try {
    if (client) {
      return client;
    }
    const response = await fetch('/api-oss/sts');
    const result = await response.json();
    if (result) {
      const { AccessKeyId, AccessKeySecret, SecurityToken } =
        result as Record<string, string>;
      client = new OSS({
        region,
        bucket,
        accessKeyId: AccessKeyId,
        accessKeySecret: AccessKeySecret,
        stsToken: SecurityToken,
        refreshSTSToken: async () => {
          const stsResponse = await fetch('/api-oss/sts');
          const refreshSTSResponse = await stsResponse.json();
          if (refreshSTSResponse) {
            const { AccessKeyId, AccessKeySecret, SecurityToken } =
              refreshSTSResponse as Record<string, string>;
            return {
              accessKeyId: AccessKeyId,
              accessKeySecret: AccessKeySecret,
              stsToken: SecurityToken,
            };
          }
          throw new Error('fail to get oss sts token!');
        },
      });
      return client;
    }
  } catch (error) {
    console.log('getClient error', error);
  }
  return null;
}
/**
 * @param {*} path 上传文件路径
 * @param {*} file 上传文件
 * @returns 上传成功后可预览路径
 */
export async function upload(path: string, file: any) {
  const arr = file.name.split('.');
  const len = arr.length;
  let url = '';
  if (len >= 2) {
    const postfix = arr[len - 1];
    arr.pop();
    const name = arr.join('');
    const fileName = `${name}_${file.uid}.${postfix}`;
    const client = await getClient();
    const headers = {
      // 指定该Object被下载时的名称
      'Content-Disposition': `filename=${encodeURIComponent(file.name)}`,
    };
    const response = await client?.put(`${path}/${fileName}`, file, {
      headers,
    });
    if (response?.url) {
      const urlPath = new URL(response.url);
      const path = urlPath.pathname;
      url = fileDomain + path;
    }
  }
  return {
    url,
  };
}

export const getFileType = (file: UploadFile) => {
  if (/\.(jpe?g|png|gif|bmp)/.test(file.url || '')) {
    return 'image';
  } else {
    return 'file';
  }
};