import { customAlphabet } from 'nanoid';

const nanoid = customAlphabet('1234567890abcdef', 50);

export function generateRandomCode(length = 10): string {
  // 根据传入的长度生成随机编码
  return nanoid(length);
}

// 生成纯数字编码
export function generateRandomNumber(length = 10): string {
  try {
    // 根据传入的长度生成随机编码
    const randomNumber = customAlphabet('1234567890', length)();
    return randomNumber;
  } catch (error) {
    console.error('Failed to generate random number:', error);
    return '';
  }
}

export function parseCookies(cookies: string) {
  const result: Record<string, string> = {};
  if (cookies) {
    const cookieList = cookies.split(';').map((item) => item.trim());
    if (cookieList.length > 0) {
      cookieList.forEach((cookie) => {
        if (cookie) {
          const cookiePair = cookie.split('=');
          if (cookiePair.length === 2) {
            result[cookiePair[0]] = cookiePair[1];
          }
        }
      });
    }
  }
  return result;
}

export const getEnv = () => {
  if (/local|uat|test/.test(location.href)) {
    return 'uat'
  }
  return 'pro'
}

export const getCookie = () => {
  const proToken = 'zkh_access_token'
  let token: string = '';
  const cookies: Record<string, string> = parseCookies(document.cookie)
  try {
    const tokens = document.cookie.match(/([a-z_]*?zkh_access_token)/g)
    const uatToken = tokens?.filter(r => ~r.indexOf('uat'))[0] || ''
    const localToken = tokens?.filter(r => ~r.indexOf('local'))[0] || ''
    if (getEnv() === 'pro') {
      token = proToken
    } else {
      token = uatToken || localToken
    }
    return cookies[token]
  } catch (err) { console.log(err) }
  return ''
};

export const isTestEnv = () => {
  return /fetest|local|localhost|uat/.test(location.host)
}