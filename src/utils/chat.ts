 
import { generateRandomCode, getEnv } from './index';
import { getCookie } from './index'; 
import { EventSourceMessage, fetchEventSource } from '@microsoft/fetch-event-source';
// import { UserInfo } from '../views/Home';
// import { UploadFile } from 'antd';
// import { EventMessge } from '../constants';

// export const API_KEY = 'app-uwFMeVR25xhwWvxXEa2fWxe2';
// export let CURRENT_API_KEY = '';
// export const API_KEY_CONFIG = {
//   API_KEY: 'app-uwFMeVR25xhwWvxXEa2fWxe2',
//   CURRENT_API_KEY: 'app-uwFMeVR25xhwWvxXEa2fWxe2',
// };

export const extractAllContents = (str: string) => {
  const regex = /<<<(.*?)>>>/g;
  const matches = [...str.matchAll(regex)];
  return matches.map(match => match[1])?.join(''); 
};

export const getAppKey = (appKeys: any, str: string) => {
  const env = getEnv();
  const _appKey = appKeys?.[str]?.[env] || ''
  return _appKey;
}

let ctrl: AbortController | null = null;

export function abort() {
  if (ctrl) {
    ctrl.abort('User cancelled the operation');
  }
}

export const sendMessages = async ({
  apiKey,
  username,
  queryData,
  messageId = generateRandomCode(),
  conversationSessionId,
  onMessage,
  onError,
  onClose,
}: {
  apiKey: string,
  username: string,
  queryData: any,
  messageId: string,
  conversationSessionId: string,
  onMessage: (data: string, id: string, conversationId: string) => void,
  onError: (error: any, messageId: string) => void,
  onClose: (data: string | object, messageId: string, conversationId: string, cardType?: string) => void,
}) => {
  let message = '';
  let conversationId = '';
  let responseData: any = {};
  ctrl = new AbortController();
  const _queryData = {...queryData}
  delete _queryData.IsOnlineSearch
  delete _queryData.IsDeepThinking
  await fetchEventSource('/api-agent/v1/chat-messages', {
    method: 'POST',
    headers: {
      Authorization: 'Bearer app-uRjqkmp8t2FwKFq81rs0hSOQ',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      inputs: {
        account_id: username || '',
        token: `Bearer ${getCookie()}`,
        msg_id: messageId,
        IsOnlineSearch: queryData.IsOnlineSearch,
        IsDeepThinking: queryData.IsDeepThinking,
        sessionId: conversationSessionId,
        userName: username,
      },
      ..._queryData
    }),
    signal: ctrl?.signal,
    openWhenHidden: true,
    onmessage: (event: EventSourceMessage) => {
      try {
        const _data = JSON.parse(event.data);
        if (_data && _data.event === 'agent_message') {
          message += _data.answer;
          conversationId = _data.conversation_id;
          onMessage(message, messageId, conversationId);
        } else if(_data && _data.event === 'error') {
          responseData = _data;
          // message = _data.message;
          // conversationId = _data.conversation_id;
          // onMessage(_data.message, messageId, conversationId, 'error');
        }
      } catch (ex) {
        console.error('Error parsing JSON:', ex);
      }
    },
    onerror: (error: any) => {
      console.error('Error in fetchEventSource:', error);
      onError(error, messageId);
      throw new Error('Error in fetchEventSource');
    },
    onclose: () => {
      console.log('onclose');
      // console.log('data', responseData);
      let data = message;
      try {
        data = JSON.parse(message)
      } catch (ex) {
        console.error('Error parsing JSON:', ex);
        data = message;
      }
      if(responseData && responseData.event === 'error') {
        onClose(responseData.message, messageId, conversationId, 'error');
      } else {
        onClose(data, messageId, conversationId);
      }
    },
  });
};