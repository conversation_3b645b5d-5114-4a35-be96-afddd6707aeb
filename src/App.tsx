import './App.css'
import { createBrowserRouter, RouterProvider } from 'react-router-dom'
import Home from './views/Home'
import Logo from './components/svg/Logo'
import { AppContext } from './context/app';
import { getCookie } from './utils';
import { useEffect, useState } from 'react';
import { Flex, Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import { useConfig } from './hooks/useConfig';
import { baseAppKeyName } from './constants';
import { getAppKey } from './utils/chat';
import { login } from './utils/sensors';
function callParentClose() {
  const parent = window.parent as any;
  try {
    if (typeof parent?.hideButton === 'function') {
      parent.hideButton();
    } else {
      console.error('hideButton方法不存在')
    }
  } catch (error) {
    console.log(error)
    parent.postMessage({
      method: 'hideButton',
    }, '*');
  }
}
function callParentFull() {
  const parent = window.parent as any;
  try {
    if (typeof parent?.fullscreen === 'function') {
      parent.fullscreen();
    } else {
      console.error('fullscreen方法不存在')
    }
  } catch (error) {
    console.log(error)
    parent.postMessage({
      method: 'fullscreen',
    }, '*');
  }
}
const router = createBrowserRouter(
  [
    {
      path: '/',
      element: <Home />,
      children: [
      ],
    },
  ],
  {
    basename: '/ai-helper',
  }
);
function App() {
  const isInIframe = window.parent!== window.self
  const { data: configData, appKeys, configMap } = useConfig();
  const [username, setUsername] = useState('');
  const [apiKeyName, setApiKeyName] = useState(baseAppKeyName);
  const [apiKey, setApiKey] = useState();
  const [conversationId, setConversationId] = useState('');
  const [isLogin, setIsLogin] = useState(true);
  const fetchUserInfo = async () => {
    const response = await fetch('/api-security-service/accounts/username', {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${getCookie()}`,
      },
    });
    const res = await response.json();
    if (res.username) {
      setUsername(res.username);
      login(res.username);
    } else {
      setIsLogin(false);
    }
  };

  useEffect(() => {
    fetchUserInfo()
  }, []);

  useEffect(() => {
    setApiKey(getAppKey(appKeys, baseAppKeyName));
  }, [appKeys])
  

  return (
    <AppContext value={{
      apiKey,
      setApiKey: setApiKey,
      apiKeyName,
      setApiKeyName,
      conversationId,
      setConversationId,
      username: username,
      configData: configData,
      configMap: configMap,
      appKeys: appKeys,
      isLogin: isLogin,
    }}>
      <div className="py-[30px] px-[24px] h-screen overflow-hidden bg-gradient-to-r from-[#F4EEFF] to-[#EEF3FF] shadow-[0px_0px_40px_rgba(24,35,70,0.32)]">
        <div className="flex items-center justify-between h-[42px] mb-[26px]">
          <div className="flex items-center">
            <div className="w-[42px] h-[42px] mr-[12px]">
              <Logo rotate={true} />
              {/* <Image src='https://static.zkh360.com/image/2025-02-21/7ef763ee90c1713139f9fbf677835f2c-1740132417482.gif' /> */}
            </div>
            <div>AI行家助手</div>
          </div>
          {isInIframe && <div className='flex'>
            <div onClick={callParentFull}  className='w-[22px] h-[22px] flex justify-center items-center cursor-pointer'>
              <i className='icon iconfont icon-zhankai mr-[16px] text-[16px] text-[#495CE6]'></i>
            </div>
            <div onClick={callParentClose}  className='w-[22px] h-[22px] bg-[#E4E3F6] rounded-[50%] flex justify-center items-center cursor-pointer'>
              <div className='w-[14px] h-[2px] bg-[#5062E7]'></div>
            </div>
          </div>}
          
        </div>
        {
          configData.length > 0 && username || !isLogin ? <RouterProvider router={router} /> : <Flex justify="center" align="center" className='h-full'>
            <Spin indicator={<LoadingOutlined spin />} size="large" />
          </Flex>
        }
        
      </div>
    </AppContext>
  )
}

export default App
