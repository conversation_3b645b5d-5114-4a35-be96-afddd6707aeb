import { getCookie } from '../utils';

export const useChat = (apiKey: string) => {
  const checkChatRateLimit = async (chatId: string, sessionId: string, hasAppointedLlm: boolean, hasConnectNet: boolean): Promise<{isAllowed: boolean, errorMsg: string}> => {
    try {
      const response = await fetch('/api-form-center/agent/session/add', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${getCookie()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          agent: 'AI行家助手-BOSS',
          apiKey,
          chatId,
          hasAppointedLlm: hasAppointedLlm ? 'Y' : 'N',
          hasConnectNet: hasConnectNet ? 'Y' : 'N',
          sessionId,
        }),
      });
      if(response.status === 401) {
        return { isAllowed: false, errorMsg: '登录已过期，请刷新页面重新登录' };
      }
      const res = await response.json();
      if (res.code === 200) {
        return { isAllowed: true, errorMsg: '' };
      } else {
        return { isAllowed: false, errorMsg: res.data };
      }
    } catch (error) {
      console.error('Failed to check rate limit:', error);
      return { isAllowed: false, errorMsg: 'Failed to check rate limit' };
    }
  };

  const closeChat = async (chatId: string, sessionId: string) => {
    try {
      const params = new URLSearchParams();
      params.append('chatId', chatId);
      params.append('sessionId', sessionId);
      
      fetch(`/api-form-center/agent/session/close?${params.toString()}`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${getCookie()}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });
    } catch (error) {
      console.error('Failed to close chat:', error);
    }
  };

  return { checkChatRateLimit, closeChat };
};
