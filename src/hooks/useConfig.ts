import { useEffect, useState } from 'react';
import { getCookie } from '../utils';
import { AppKeys, ConfigMap } from '../types/config';
import { PageSourceConfig } from '../constants';

type PageSourceKeys = keyof typeof PageSourceConfig;

let _data: any = [];
const _configMap: ConfigMap = {
  chatRemind: {
    configType: 'AI_HELPER_BOSS_WELCOME_CARD',
    configName: 'AI行家助手_BOSS欢迎卡片',
  },
  appKey: {
    configType: 'AgentApiKeyConfig',
    configName: '智能体ApiKey配置',
  }
};
export const useConfig = () => {
  const [data, setData] = useState(_data);
  const [appKeys, setAppKeys] = useState<AppKeys | null>(null);
  const [configMap, setConfigMap] = useState<ConfigMap>(_configMap);

  useEffect(() => {
    const fetchTemplateQuery = async (configData: any) => {
      const response = await fetch(
        '/api-form-center/front-end/display/template/query',
        {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${getCookie()}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...configData,
          }),
        }
      );
      if(response.status === 401) {
        return {code: 401, data: '登录已过期，请刷新页面重新登录'};
      }
      return response.json(); // 返回解析后的 JSON 数据
    };

    // 根据域账号查询配置key
  const getConfigMap = async () => {
    try {
      const res = await fetch(
        '/api-opc/ai/judgeABTestByUsername',
        {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${getCookie()}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            projectNameList:  ['chatRemind','appKey'],
          }),
        }
      );
      const resData = await res.json();
      if (resData.code === 200 && resData.data?.projectToConfigMap) {
        const transformedConfigMap: ConfigMap = Object.fromEntries(
          Object.entries(resData.data.projectToConfigMap).map(([key, value]) => {
            console.log('value', value)
            const [configType, configName] = (typeof value === 'string' ? value : '').split(':');
            return [key, { configType, configName }];
          })
        );
        setConfigMap(transformedConfigMap);
        return transformedConfigMap;
      }
      return configMap;
    } catch (error) {
      console.error(error);
    }
  }

  const getPageSource = () => {
    const params = new URLSearchParams(window.location.search);
    const source = params.get('pageSource') as PageSourceKeys;
    return source?.toLocaleUpperCase() || '';
  }
  const getPageSourceConfig = (source: PageSourceKeys) => {
    if (source && PageSourceConfig[source]) {
      return PageSourceConfig[source]
    }
    return null
  };
    const query = async () => {
      try {
        let configMap = null;
        // 外部系统嵌入，单独配置
        const pageSource = getPageSource() as PageSourceKeys;
        const pageSourceConfig = getPageSourceConfig(pageSource);
        if (pageSource && pageSourceConfig) {
          configMap = pageSourceConfig;
        } else {
          configMap = await getConfigMap();
        }
        console.log('configMap', configMap)
        const configData = {
          configType: configMap?.chatRemind?.configType,
          customizedAlias: configMap?.chatRemind?.configName,
          isDefault: 'Y',
          keyJson: {
            formName: configMap?.chatRemind?.configName,
            formType: configMap?.chatRemind?.configType,
            configType: configMap?.chatRemind?.configType,
          },
          keyResolveRule: 'configType',
          owner: 'SYS',
        };
        const appKeyConfigData = {
          configType: configMap?.appKey?.configType,
          customizedAlias: configMap?.appKey?.configName,
          isDefault: 'Y',
          keyJson: {
            formName: configMap?.appKey?.configName,
            configType: configMap?.appKey?.configType,
          },
          keyResolveRule: 'configType-formName',
          owner: 'SYS',
        };
        const [configRes, appKeyRes] = await Promise.all([
          fetchTemplateQuery(configData),
          fetchTemplateQuery(appKeyConfigData),
        ]);
        if (configRes.code === 200 && configRes.data.records.length) {
          setData(configRes.data.records);
          _data = configRes.data.records;
        }
        if (appKeyRes.code === 200 && appKeyRes.data.records.length) {
          const appKeyData = appKeyRes.data.records.find((item: any) => item.configType === configMap?.appKey?.configType || 'AgentApiKeyConfig') || {};
          setAppKeys(appKeyData.templateInfo);
        }
      } catch (error) {
        console.error('Failed to get initial help message:', error);
      }
    };
    if (data.length === 0) {
      query();
    }
  }, []);
  return {
    data,
    appKeys,
    configMap,
  };
};
