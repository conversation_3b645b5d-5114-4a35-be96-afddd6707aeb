import { useEffect, useState } from 'react';
import { getCookie } from '../utils';
import { AppKeys } from '../types/config';

let _data: any = [];
export const useConfig = () => {
  const [data, setData] = useState(_data);
  const [appKeys, setAppKeys] = useState<AppKeys | null>(null);

  useEffect(() => {
    const fetchTemplateQuery = async (configData: any) => {
      const response = await fetch(
        '/api-form-center/front-end/display/template/query',
        {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${getCookie()}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...configData,
          }),
        }
      );
      if(response.status === 401) {
        return {code: 401, data: '登录已过期，请刷新页面重新登录'};
      }
      return response.json(); // 返回解析后的 JSON 数据
    };
    const query = async () => {
      try {
        const configData = {
          configType: 'AI_HELPER_BOSS_WELCOME_CARD',
          customizedAlias: 'AI行家助手_BOSS欢迎卡片',
          isDefault: 'Y',
          keyJson: {
            formName: 'AI行家助手_BOSS欢迎卡片',
            formType: 'AI_HELPER_BOSS_WELCOME_CARD',
            configType: 'AI_HELPER_BOSS_WELCOME_CARD',
          },
          keyResolveRule: 'configType',
          owner: 'SYS',
        };
        const appKeyConfigData = {
          configType: 'AgentApiKeyConfig',
          customizedAlias: '智能体ApiKey配置',
          isDefault: 'Y',
          keyJson: {
            formName: '智能体ApiKey配置',
            configType: 'AgentApiKeyConfig',
          },
          keyResolveRule: 'configType-formName',
          owner: 'SYS',
        };
        const [configRes, appKeyRes] = await Promise.all([
          fetchTemplateQuery(configData),
          fetchTemplateQuery(appKeyConfigData),
        ]);
        console.log('configRes', configRes)
        if (configRes.code === 200 && configRes.data.records.length) {
          setData(configRes.data.records);
          _data = configRes.data.records;
        }
        if (appKeyRes.code === 200 && appKeyRes.data.records.length) {
          const appKeyData = appKeyRes.data.records.find((item: any) => item.configType === 'AgentApiKeyConfig') || {};
          setAppKeys(appKeyData.templateInfo);
        }
      } catch (error) {
        console.error('Failed to get initial help message:', error);
      }
    };
    if (data.length === 0) {
      query();
    }
  }, []);
  return {
    data,
    appKeys,
  };
};
