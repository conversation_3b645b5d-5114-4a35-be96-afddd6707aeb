import { useEffect, useState } from 'react';

export const useIframeInitializer = () => {
  const isInIframe = window.parent!== window.self
  const [iframeMessage, setIframeMessage] = useState('');

  const handleIframeMessage = () => {
    const parentLocation = window.parent.location
    const parentUrl = parentLocation.href;
    // 订单详情页打开，自动带入订单号
    if (parentUrl.includes('/orderSale/formal/detail/')) {
      const urlParams = new URLSearchParams(parentLocation.search);
      const soNo = urlParams.get('soNo') || '';
      if (soNo) {
        setIframeMessage(`销售订单号：${soNo}`)
      }
    }
  }

  useEffect(() => {
    if (isInIframe) {
        // 处理在iframe中的逻辑
        handleIframeMessage()
      }
  }, [isInIframe]);


  return {
    isInIframe,
    iframeMessage,
  }
}