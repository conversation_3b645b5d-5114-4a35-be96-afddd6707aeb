import { createContext } from 'react';
import { AppKeys } from '../types/config';
import { baseAppKeyName } from '../constants';

interface AppContextType {
  apiKey: string;
  setApiKey: (apiKey: string) => void;
  apiKeyName: string;
  setApiKeyName: (apiKeyName: string) => void;
  conversationId: string;
  setConversationId: (conversationId: string) => void;
  username: string
  configData: any,
  appKeys: AppKeys | null,
  isLogin: boolean,
}

export const AppContext = createContext<AppContextType>({
  apiKey: '',
  setApiKey: () => {},
  apiKeyName: baseAppKeyName,
  setApiKeyName: () => {},
  conversationId: '',
  setConversationId: () => {},
  username: '',
  configData: null,
  appKeys: null,
  isLogin: true,
});
