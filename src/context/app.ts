import { createContext } from 'react';
import { AppKeys, ConfigMap } from '../types/config';
import { baseAppKeyName } from '../constants';

interface AppContextType {
  apiKey: string;
  setApiKey: (apiKey: string) => void;
  apiKeyName: string;
  setApiKeyName: (apiKeyName: string) => void;
  conversationId: string;
  setConversationId: (conversationId: string) => void;
  username: string
  configData: any,
  configMap: ConfigMap | null,
  appKeys: AppKeys | null,
  isLogin: boolean,
}

export const AppContext = createContext<AppContextType>({
  apiKey: '',
  setApiKey: () => {},
  apiKeyName: baseAppKeyName,
  setApiKeyName: () => {},
  conversationId: '',
  setConversationId: () => {},
  username: '',
  configData: null,
  configMap: null,
  appKeys: null,
  isLogin: true,
});
