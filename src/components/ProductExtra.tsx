import React from 'react';
export interface ProductExra {
  desc: string
  tags: string[];
}
interface ProductCardProps {
  item: ProductExra;
}
const ProductExtra: React.FC<ProductCardProps> = ({ item }) => {

  return (
    <div style={{backgroundColor: '#fff', borderRadius: '0 20px 20px 20px', padding: '20px'}}>
      <div style={{color: '#333', fontSize: '14px', lineHeight: '30px', marginBottom: '10px'}}>
        您对颜色有要求吗? 比如：白色、蓝色{item.desc}
      </div>
      <div style={{color: '#333', fontSize: '14px', lineHeight: '30px', marginBottom: '10px'}}>
        您对是否独立包装有要求吗? 比如：否、是
      </div>
    </div>
  );
};

export default ProductExtra;