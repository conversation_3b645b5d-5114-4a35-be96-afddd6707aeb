import React, { useContext, useEffect, useRef, useState } from 'react';
import { Bubble, BubbleProps } from '@ant-design/x';
import { Spin } from 'antd';
import { MessageType } from '../constants';
import Logo from './svg/Logo'
import { AppContext } from '../context/app';
import { getCookie } from '../utils';


interface Props {
  text: string;
  type: MessageType;
  status?: string;
  apiKeyName?: string;
  sendIframeMessage: (source: string, message: string, flowData?: any) => void;
}

const RPAIframeCard: React.FC<Props> = ({ text, type, apiKeyName: messageApiKeyName, sendIframeMessage }) => {
  const { apiKeyName } = useContext(AppContext);
  const [iframeHeight, setIframeHeight] = useState<number>(200);
  const [loading, setLoading] = useState<boolean>(true);

  const iframeRef = useRef<HTMLIFrameElement>(null);
  
  const handleSelectFlow = async (source: string, data: any) => {
    const flowId = data.process_select;
    // 通过流程id获取流程json
    const response = await fetch('/api-rpa/flow/list', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${getCookie()}`,
      },
      body: JSON.stringify({
        id: flowId
      })
    })
    const resData = await response.json();
    console.log('resData', resData);
    const flowData = resData?.data?.rows?.find(item => item.id === flowId);
    const url = flowData?.runParamsFile;
    const res = await fetch(url, {
      method: 'GET',
    })
    const jsonData = await res.json();
    const query = `使用这个流程，将json转换成jsx代码\n${JSON.stringify(jsonData)}`
    const _params = {
      flowId: flowData?.id,
      name: flowData?.name,
      runTimeType: '1',
      groupId: flowData?.groupId,
      robotUuid: flowData?.flowId,
    };
    sendIframeMessage(source, query, _params);
  }

  const handleEditFlow = async (source: string, data: any) => {
    console.log('编辑流程:', data);
    const query = '根据这段json，发起rpa任务'
    sendIframeMessage(source, query, data);
  }

  const handleMessage = async (event) => {
    console.log('收到消息:', event.data);
    const { source, data } = event.data;
    switch (source) {
      case 'selectFlow':
          handleSelectFlow(source, data);
          break;
      case 'editFlow':
          handleEditFlow(source, data);
          break;
      default:
          console.log('未知的消息源:', source);
          break;
    }
  };

  useEffect(() => {
    window.addEventListener('message', handleMessage);
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  const handleLoad = () => {
    if (iframeRef.current) {
      const iframeDocument = iframeRef.current.contentDocument;
      if (iframeDocument) {
        const height = Math.max(
          iframeDocument.body.scrollHeight,
          iframeDocument.documentElement.scrollHeight
        );
        setIframeHeight(height); // 更新状态
        setLoading(false);
      }
    }
  };

  const avatar = type === 'answer'
    ? <div className="flex items-center"><Logo style={{ width: '30px', height: '30px' }} /><div className="text-[13px] text-[#8A8A8A] ml-[10px]">{messageApiKeyName || apiKeyName}</div></div>
    : undefined
  if (type === 'system') {
    return <div className="flex justify-center border-[1px] border-[#ccc] border-dashed px-2 my-3 w-full">
    </div>
  }

  const renderStr: BubbleProps['messageRender'] = (content) => {
  return (
    <Spin spinning={loading} tip="正在加载中">
      <iframe
        ref={iframeRef}
        srcDoc={text}
        onLoad={handleLoad}
        style={{ width: '100%', height: `${iframeHeight}px`, border: 'none' }}
        title="Embedded Content"
      />
    </Spin>
    )
  }


  return (
    <Bubble
      typing={false}
      className={type === 'answer' ? 'answer-bubble' : 'question-bubble'}
      placement={type === 'answer' ? 'start' : 'end'}
      content={text}
      messageRender={renderStr}
      avatar={avatar}
    />
  );
};

export default RPAIframeCard;