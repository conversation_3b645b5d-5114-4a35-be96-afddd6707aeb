import React from 'react';
import { Attachments } from '@ant-design/x';
import { UploadFile, Image } from 'antd';
import { MessageType } from '../constants';
import { getFileType } from '../utils/upload';


interface Props {
  fileList: UploadFile[];
  type: MessageType;
}

const FileCard: React.FC<Props> = ({ fileList }) => {
  return (
    <div className='flex flex-wrap justify-end'>
      {fileList.map((file) => (
        getFileType(file) === 'image'
          ? <Image src={file.url} width={100} height={100} />
          : <div className='cursor-pointer' onClick={() => window.open(file.url, '_blank')}>
              <Attachments.FileCard key={file.uid} item={file} />
            </div>
      ))}
    </div>
  )
};

export default FileCard;