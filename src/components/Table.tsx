import { Table } from "antd";

interface Props {
  data: any[];
}

const columns = [
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '年龄',
    dataIndex: 'age',
    key: 'age',
  },
  {
    title: '住址',
    dataIndex: 'address',
    key: 'address',
  },
];

export const TableComponent: React.FC<Props> = (props) => {
  return <Table dataSource={props.data} columns={columns} />;
};


