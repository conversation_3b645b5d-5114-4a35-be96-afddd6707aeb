import React from 'react';
import { Image } from 'antd';
import { isTestEnv } from '../utils/index'
import defaultSku from '@/assets/images/defaultSku.png'
export interface ProductItem {
  sku: string;
  productName: string;
  name?: string;
  price: number;
  imagePath?: string;
}
interface ProductCardProps {
  item: ProductItem;
}
const jumpGw = (skuNo: string) => {
  window.open(`https://${isTestEnv() ? 'webuat' : 'www'}.zkh.com/item/${skuNo}.html?proSkuNo=${skuNo}`)
}
const jumpPc = (skuNo: string) => {
  window.open(`https://pc${isTestEnv() ? '-uat' : ''}.zkh360.com/product/skuDetail/${skuNo}`)
}
const ProductCard: React.FC<ProductCardProps> = ({ item }) => {

  return (
    <div className='flex items-center justify-center mb-[20px]'>
      <Image style={{ flexShrink: 0, width: '80px', height: '80px', borderRadius: '10px', border: '1px solid #EBEEF5', marginRight: '10px' }} src={item.imagePath || defaultSku} />
      <div style={{ flex: 1 }}>
        <div className='w-[100%] multi-line-2' style={{ fontSize: '14px', color: '#333', lineHeight: '24px',  marginBottom: '4px' }}>{item.productName || item.name}</div>
        <div className='flex items-center justify-between'>
          <div style={{ fontSize: '12px',color: '#333', lineHeight: '22px' }}>¥
            <span style={{ fontSize: '18px', fontWeight: 'bold', color: '#F74747' }}>{item.price}</span>
          </div>
          <div className='text-[#878BA6] text-[12px]'>
            <span className='mr-[20px] cursor-pointer' onClick={() => jumpGw(item.sku)}>查官网</span>
            <span className='cursor-pointer' onClick={() => jumpPc(item.sku)}>看商品详情</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductCard;