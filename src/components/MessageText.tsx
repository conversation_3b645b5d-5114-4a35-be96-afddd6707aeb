import React, { useContext } from 'react';
import { Bubble, BubbleProps } from '@ant-design/x';
import { Typography } from 'antd';
import { MessageType } from '../constants';
import markdownit from 'markdown-it';
import Logo from './svg/Logo'
import { AppContext } from '../context/app';
import LoadingDots from './svg/LoadingDots';
import 'github-markdown-css';

const md = markdownit({ html: true, breaks: true });

interface Props {
  text: string;
  type: MessageType;
  status?: string;
  apiKeyName?: string;
}

// 自定义插件
md.use((md: any) => {
  const defaultRender = md.renderer.rules.link_open || function (tokens: any, idx: any, options: any, env: any, self: any) {
    return self.renderToken(tokens, idx, options);
  };

  md.renderer.rules.link_open = function (tokens: any, idx: any, options: any, env: any, self: any) {
    // 添加 target 属性
    tokens[idx].attrPush(['target', '_blank']);
    return defaultRender(tokens, idx, options, env, self);
  };
});



const MessageText: React.FC<Props> = ({ text, type, apiKeyName: messageApiKeyName, status }) => {
  const { apiKeyName } = useContext(AppContext);
  const avatar = type === 'answer'
    ? <div className="flex items-center"><Logo style={{ width: '30px', height: '30px' }} /><div className="text-[13px] text-[#8A8A8A] ml-[10px]">{messageApiKeyName || apiKeyName}</div></div>
    : undefined
  if (type === 'system') {
    return <div className="flex justify-center border-[1px] border-[#ccc] border-dashed px-2 my-3 w-full">
    </div>
  }

  const renderStr: BubbleProps['messageRender'] = (content) => {
    if (status === 'waiting') {
      return <LoadingDots content={content} />
    }
    return (
      <Typography>
        <div className="markdown-body" dangerouslySetInnerHTML={{ __html: md.render(content) }} />
      </Typography>
    )
  }
  
  return (
    <Bubble
      typing={false}
      className={type === 'answer' ? 'answer-bubble' : 'question-bubble'}
      placement={type === 'answer' ? 'start' : 'end'}
      content={text}
      messageRender={renderStr}
      avatar={avatar}
    />
  );
};

export default MessageText;