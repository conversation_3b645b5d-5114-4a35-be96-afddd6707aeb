import { Space, Popover } from 'antd';
// import { OpenAIOutlined } from '@ant-design/icons';
import { UploadFile } from 'antd/es/upload/interface';
import { Sender, Attachments } from '@ant-design/x';
import TabNavigation from './TabNavigation';
import expand from '@/assets/images/expand.png';
import close from '@/assets/images/close.png';
import { getFileType } from '../utils/upload';
import { useRef } from 'react';
import { CloudUploadOutlined } from '@ant-design/icons';

interface SenderProps {
  inputText: string;
  setInputText: (text: string) => void;
  onSend: (message: string) => void;
  loading: boolean;
  setLoading: (loading: boolean) => void;
  handleUploadFile: (options: any) => Promise<void>;
  handleUpload: (info: any) => void;
  fileList: UploadFile[];
  afterRemove: (file: UploadFile, event: React.MouseEvent) => void;
  children: any
  onItemClick: (item: string) => void;
  isMessagePending: () => boolean;
}
const content = (
  <div className="text-[#6F7496] text-[12px] font-[400]">
    <div>支持拖拽上传附件/图片</div>
    <div>图片一次一张，大小不超过4M</div>
    <div>支持jpg、png等</div>
  </div>
);

const SenderEditor: React.FC<SenderProps> = ({ inputText, setInputText, onSend, loading, setLoading, handleUploadFile, handleUpload, fileList, afterRemove, children, onItemClick, isMessagePending }) => {
  const senderRef = useRef<HTMLDivElement>(null);
  
  const handleSend = () => {
    if (inputText.trim()) {
      onSend(inputText);
      setInputText('');
    }
  };

  const formatFileSize = (sizeInBytes: number) => {
    if (sizeInBytes < 1024 * 1024) {
        return (sizeInBytes / 1024).toFixed(2) + ' K';
    } else {
        return (sizeInBytes / (1024 * 1024)).toFixed(2) + ' M';
    }
  }

  return (
    <div className="relative">
      {fileList.length > 0 ? (
        <div className="w-full flex h-[80px] bg-[#E0E5F8] rounded-t-[10px] absolute top-[-65px] ">
        {fileList.map((file) => (
          <div key={file.uid} className="mr-2 mt-[20px] cursor-pointer" onClick={() => {
            window.open(file.url, '_blank');
          }}>
            <div className="relative w-[150px] h-[34px] mx-[16px] bg-[#fff] rounded-[5px] flex items-center justify-between pr-[15px] pl-[10px]">
              <img src={getFileType(file) === 'image' ? file.url : expand} alt="preview" className="w-[13px] h-[13px] mr-[5px]"></img>
              <div className="text-[12px] font-[400] text-[#6F7496] flex-1 overflow-hidden text-ellipsis whitespace-nowrap mr-[5px]">{file.name}</div>
              <div className="text-[12px] font-[400] text-[#6F7496]">{formatFileSize(file.size || 0)}</div>
              <img
                src={close}
                alt="close"
                className="w-[14px] h-[14px] absolute right-[-5px] top-[-6px]"
                onClick={(e) => afterRemove(file, e)}
              />
            </div>
          </div>
        ))}
      </div>
      ) : (
        <TabNavigation onItemClick={onItemClick} disabled={isMessagePending()} />
      )}
      <div ref={senderRef}>
        <Sender
          submitType="enter"
          placeholder="Shift + Enter键换行"
          className="h-[160px] z-10 sender-content bg-white"
          value={inputText}
          loading={loading}
          onChange={setInputText}
          disabled={isMessagePending()}
          onSubmit={() => {
            setLoading(true);
            handleSend();
          }}
          onCancel={() => {
            setLoading(false);
          }}
          actions={(_, info) => {
            const { SendButton, LoadingButton } = info.components;
            const disabled = fileList.length > 0 || isMessagePending();
            return (
              <>
                {children}
                <Space size="middle">
                  <Attachments
                    action="/api-opc/common/file/uploadFile"
                    beforeUpload={() => {
                      if (disabled) return false;
                      return true;
                    }}
                    onChange={handleUpload}
                    getDropContainer={() => senderRef?.current}
                    placeholder={{
                      icon: <CloudUploadOutlined />,
                      title: '文件拖动到此处可上传',
                      description: '支持图片、pdf、doc、docx、xls、xlsx、ppt、pptx、txt等格式，单次对话仅支持上传1个文件',
                    }}
                    rootClassName={`${disabled ? 'pointer-events-none' : 'cursor-pointer'}`}
                  >
                    <Popover placement="top" content={content}>
                      <span title='上传附件' className={'icon iconfont h-[15px]'}>&#xe690;</span>
                    </Popover>
                  </Attachments>
                  {loading ? (
                    <LoadingButton type="default" disabled />
                  ) : (
                    <SendButton id="ai_helper_send_button" type="primary" icon={<span title='发送' className="icon iconfont cursor-pointer h-[15px]">&#x10118;</span>} disabled={false} />
                  )}
                </Space>
              </>
            );
          }}
        />

      </div>
    </div>
  );
};

export default SenderEditor;