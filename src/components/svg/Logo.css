.svglogo {
  position: relative;
  width: 42px;
  height: 42px;
  margin: 0 auto;
}

.background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: linear-gradient(144deg, #af40ff, #5b42f3 50%, #00ddeb);
}

.rotate {
  animation: rotate 3s linear infinite;
}

.logo-image {
  width: 80%;
  height: 80%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
 
}

@keyframes rotate {
  0% {
      transform: rotate(0deg);
  }
  100% {
      transform: rotate(360deg);
  }
}