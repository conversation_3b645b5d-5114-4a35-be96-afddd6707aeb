import './LoadingDots.css';

const LoadingDots = ({ content }: { content: string, apiKeyName?: string }) => {

  return (
    <div className='flex'>
        <div className="text-[#495CE6] text-[12px] mr-[8px]">{content}</div>
        <div className="spinner-box mr-[8px]">
          <div className="pulse-container">  
            <div className="pulse-bubble pulse-bubble-1"></div>
            <div className="pulse-bubble pulse-bubble-2"></div>
            <div className="pulse-bubble pulse-bubble-3"></div>
          </div>
        </div>
      </div>
  );
};

export default LoadingDots;