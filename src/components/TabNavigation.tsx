import React, { useState, useContext, useRef, useEffect  } from 'react';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { AppContext } from '../context/app';

const Navigation: React.FC<{ onItemClick: (item: string) => void, disabled: boolean }> = ({ onItemClick, disabled }) => {
  const { configData } = useContext(AppContext);
  const helpMessage = (configData || []).find((item: any) => item.configType === 'AI_HELPER_BOSS_WELCOME_CARD');
  const data: any = helpMessage?.templateInfo?.tabNavigation || [];

  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAllEntry, setIsAllEntry] = useState(false);
  const itemsToShow = 2.5; // 每次显示的项目数量

  const handlePrev = () => {
    setCurrentIndex((prevIndex) => Math.max(prevIndex - itemsToShow, 0));
  };

  const handleNext = () => {
    console.log(isNextDisabled,currentIndex,trx,data.length)
    if (isNextDisabled) return;
    setCurrentIndex((prevIndex) => {
      const newIndex = prevIndex + itemsToShow;
      return Math.min(newIndex, data.length); // 确保不超出范围
    });
  };

  const isPrevDisabled = currentIndex === 0;
  const isNextDisabled = isAllEntry || currentIndex >= data.length;
  const rootRef = useRef(null);
  const lastDivRef = useRef(null);
  const trx = currentIndex / itemsToShow;
  useEffect(() => {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
          if (entry.intersectionRatio > 0.95) {
              console.log('最右边元素全部进入了视图中');
              setIsAllEntry(true)
          } else if (entry.intersectionRatio > 0) {
              console.log('最右边元素进入了视图中');
              setIsAllEntry(false)
          } else {
            setIsAllEntry(false)
          }
      });
    }, {
        root: rootRef.current,
        threshold: 0.9
    });
    if (lastDivRef.current) {
      observer.observe(lastDivRef.current);
    }
  }, [])

  return (
    <div ref={rootRef} style={{ display: 'flex', alignItems: 'center', padding: '10px', borderRadius: '8px' }}>
      <div 
        onClick={handlePrev}
        className={`w-[8px] h-[14px] mr-[20px] flex items-center justify-center ${isPrevDisabled ? 'cursor-not-allowed text-[gray]' : 'cursor-pointer text-[#495CE6]'}`}
      >
        <LeftOutlined color="#495CE6" />
      </div>
      <div className={`flex-1 overflow-hidden whitespace-nowrap ${disabled ? 'pointer-events-none' : 'cursor-pointer'}`}>
        <div className="flex items-center justify-between cursor-pointer transition-transform duration-300 ease-in-out" style={{ transform: `translateX(-${trx * 50}%)` }}>
          {data.map((item: any, index: number) => (
            <div id={`ai_helper_tab_navigation_${item.name}_button`} ref={index === data.length - 1 ? lastDivRef : null} onClick={() => onItemClick(item)} key={index} className="inline-block text-center bg-[#ffffff] rounded-[13px] h-[26px] mr-[16px] px-[8px] text-[12px] font-[#333333] leading-[26px]" >
              {item.name}
            </div>
          ))}
        </div>
      </div>
      <div 
        onClick={handleNext}
        className={`w-[8px] h-[14px] ml-[20px] flex items-center justify-center ${isNextDisabled ? 'cursor-not-allowed text-[gray]' : 'cursor-pointer text-[#495CE6]'}`}
      >
        <RightOutlined />
      </div>
    </div>
  );
};

export default Navigation;