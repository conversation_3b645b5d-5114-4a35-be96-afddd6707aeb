import { useEffect, useState } from "react";

type ItemData = {
  label: string;
  value: string;
};

type RowData = ItemData | string;

type TableData = RowData[];

interface Props {
  data: TableData;
}

interface RowProps {
  data: RowData;
}

const SalesRow: React.FC<RowProps> = (props) => {
  const data = props.data;
  const len = typeof data === "string" ? 1 : 2;
  if (len === 1) {
    return (
      <div className="h-[30px] font-[12px] text-[#757575] px-1">
        {data as string}
      </div>
    );
  } else {
    return (
      <div className="flex h-[30px] font-[12px] text-[#757575]">
        <div className="w-[25%] border-r-[1px] border-[#DBDBF1] px-1">{(data as ItemData).label}</div>
        <div className="flex-1 px-1">{(data as ItemData).value}</div>
      </div>
    );
  }
};

const displayTypingEffect = (src: TableData, updateFn: (data: RowData) => void , callback: () => void) => {
    let index = 0;
    const typingInterval = setInterval(() => {
      if (index < src.length) {
        updateFn(src[index])
        index++;
        callback();
      } else {
        clearInterval(typingInterval); // 完成后清除定时器
      }
    }, 100);
  };

const SalesTable: React.FC<Props> = (props) => {
  const [data, setData] = useState<TableData>([]);
  const update = (data: RowData) => {
    setData((prevData) => [...prevData, data]);
  };
  useEffect(() => {
    displayTypingEffect(props.data, update, () => {})
  }, [props.data]);
  return (<div className="border-[1px] border-[#DBDBF1]">
    {
      data.map((row, index) => (<div className="border-b-[1px] border-[#DBDBF1]"><SalesRow key={index} data={row} /></div>))
    }
  </div>);
};

export default SalesTable;
