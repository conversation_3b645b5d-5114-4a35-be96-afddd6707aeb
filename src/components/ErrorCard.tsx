/**
 * @Author: luozhi<PERSON>
 * @Date: 2025-02-24 15:09:47
 * @LastEditors: luozhikai
 * @LastEditTime: 2025-02-24 17:35:06
 * @Description: file content
 */
import { CaretDownOutlined, CaretRightOutlined } from '@ant-design/icons';
import { Bubble, BubbleProps } from '@ant-design/x';
import React, { useState } from 'react';
import Logo from './svg/Logo';

interface Props {
  msg: string;
}


const ErrorCard: React.FC<Props> = ({ msg }) => {
  const [expandMsg, setExpandMsg] = useState(false)

  const renderError: BubbleProps['messageRender'] = (content) => (
    <div className='text-[14px] text-[#8a8a8a]' >
      <div className='flex justify-start items-center cursor-pointer select-none' onClick={() => setExpandMsg(!expandMsg)}>
        {expandMsg ? (
          <CaretDownOutlined />
        ) : (
          <CaretRightOutlined />
        )}
        <span>服务失败，请选择场景后重试</span>
      </div>
      {expandMsg && <div>{content}</div>}
    </div>
  )
  return (
    <Bubble
      typing={false}
      className='answer-bubble'
      placement='start'
      content={msg}
      messageRender={renderError}
      avatar={<Logo />}
    />
  );
};

export default ErrorCard;