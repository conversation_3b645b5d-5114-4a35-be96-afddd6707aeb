import React, { useContext } from 'react';
import { AppContext } from '../context/app';
import { HelpContent } from '../types/config';
import { QuestionCircleOutlined } from '@ant-design/icons';


const HelpCard: React.FC<{ content: string; onItemClick: (item: any) => void }> = ({ onItemClick }) => {
  const { configData } = useContext(AppContext);
  const helpMessage = (configData || []).find((item: any) => item.configType === 'AI_HELPER_BOSS_WELCOME_CARD');
  const data: HelpContent = helpMessage?.templateInfo || {}
  return (
    <div className="text-[14px]">
      <div className="p-[24px] bg-gradient-to-r from-[#DAD3FA] to-[#D4E7FD] rounded-[0px] rounded-tr-[20px]">
        <div className="">{data.greeting}</div>
        <p className="mt-2 text-gray-700">{data.description}</p>
      </div>
      <div className="p-[24px] bg-white shadow-[0px_0px_12px_0px_rgba(24,35,70,0.05)] rounded-bl-[20px] rounded-br-[20px]">
        {(data.helpTopics || []).map((topic, index) => (
          <div key={index}>
            <p className="text-[#8A8A8A]">{topic.title}</p>
            <div className="mt-2">
              {topic.items.map((topicItem, idx) => (
                <div key={idx} className="cursor-pointer">
                  <QuestionCircleOutlined className="mr-2" style={{ color: '#495CE6' }} />
                  {topicItem.title}：{topicItem.items.map((item, idx) => (
                    <span key={idx} className="text-[#495CE6]">
                      <span onClick={() => onItemClick(item)}>{item.name}</span>
                      {idx < topicItem.items.length - 1 ? '、' : ''}</span>
                  ))}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default HelpCard;