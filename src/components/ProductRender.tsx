import React, { useContext } from 'react';
import { Bubble } from '@ant-design/x';
import { MessageType } from '../constants';
import Logo from './svg/Logo'
import { AppContext } from '../context/app';

interface Props {
  text: string;
  type: MessageType;
  apiKeyName?: string;
  renderProducts: (data: any) => React.ReactNode;
}

const MessageProduct: React.FC<Props> = ({ text, type, renderProducts, apiKeyName: messageApiKeyName }) => {
  const { apiKeyName } = useContext(AppContext);

  const avatar = type === 'answer'
    ? <div className="flex items-center"><Logo style={{ width: '30px', height: '30px' }} /><div className="text-[13px] text-[#8A8A8A] ml-[10px]">{messageApiKeyName || apiKeyName}</div></div>
    : undefined

  return (
    <Bubble
      typing={false}
      placement={type === 'answer' ? 'start' : 'end'}
      content={text}
      messageRender={renderProducts}
      avatar={avatar}
    />
  );
};

export default MessageProduct;