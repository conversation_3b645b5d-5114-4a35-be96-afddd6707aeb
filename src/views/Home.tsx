import { Button, Flex, UploadFile, UploadProps, message } from 'antd';
import { useContext, useEffect, useRef, useState } from 'react';
import { upload } from '../utils/upload';
import SenderEditor from '../components/SenderEditor';
import { generateRandomCode, generateRandomNumber, getCookie, isTestEnv } from '../utils';
import HelpCard from '../components/HelpCard';
import MessageText from '../components/MessageText';
import { MessageType, CardType, MessageStatus, baseAppKeyName, MESSAGE, DEEPSEEK_APP_KEY_NAME } from '../constants';
import { sendMessages, extractAllContents, abort, getAppKey } from '../utils/chat';
import { useChat } from '../hooks/useChat';
import { AppContext } from '../context/app';
import ProductCard from '../components/ProductCard';
import MessageProduct from '../components/ProductRender';
import FileCard from '../components/FileCard'
import ErrorCard from '../components/ErrorCard';
import RPAIframeCard from '../components/RPAIframeCard';

export interface Message {
  messageType: MessageType;
  cardType?: CardType;
  content: string | Record<string, any>;
  status?: MessageStatus;
  id: string;
  products?: any[];
  apiKey?: string;
  apiKeyName?: string;
  chatId?: string;
}

export interface MessageData {
  data: Message[];
  apiKey: string;
  conversationId?: string;
}
const Home: React.FC = () => {
  const { username, configData, appKeys, apiKey, apiKeyName, setApiKey, conversationId, setConversationId, setApiKeyName, isLogin } = useContext(AppContext);
  const { checkChatRateLimit, closeChat } = useChat(apiKey);
  const [loading, setLoading] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState<string>('');
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [hasAppointedLlm, setHasAppointedLlm] = useState<boolean>(false);
  const [hasConnectNet, setHasConnectNet] = useState<boolean>(false);
  const [sessionId, setSessionId] = useState<string>('');
  const [messageApi, contextHolder] = message.useMessage();
  const [conversationSessionId, setConversationSessionId] = useState<string>(''); // 每次会话的sessionId
  const [flowData, setFlowData] = useState<any>(null); // rpa流程数据

  // 判断是否还有消息正在发送
  const isMessagePending = () => {
    return loading || messages.some(message => message.status === 'pending' || message.status === 'waiting');
  }

  useEffect(() => {    
    setSessionId(generateRandomCode(50)); // 每一个会话窗口生成一个随机码
    setConversationSessionId(generateRandomNumber(10));
    // 当组件首次加载时，推送帮助消息
    if (messages.length === 0) {
      if(isLogin) {
        const helpMessage: Message = {
          cardType: 'help',
          messageType: 'answer',
          content: JSON.stringify(configData.templateInfo),
          id: generateRandomCode(),
          apiKey,
        };
        setMessages([helpMessage]);
      } else {
        const helpMessage: Message = {
          cardType: 'text',
          messageType: 'answer',
          content: '登录已过期，请刷新页面重新登录',
          id: generateRandomCode(),
          apiKey,
        };
        setMessages([helpMessage]);
      }
    }
  }, [isLogin]);

  useEffect(() => {
    if (loading) {
      const timer = setTimeout(() => {
        setLoading(false);
        setInput('');
      }, 2000);
      return () => {
        clearTimeout(timer);
      };
    }
  }, [loading]);

  const clearChat = () => {
    setMessages([]);
    setInput('');
    setFileList([]);
    closeChat('', sessionId);
  };

  const newChat = () => {
    setMessages((prevMessages) => {
      return [...prevMessages, {
        messageType: 'system',
        cardType: 'text',
        content: '',
        id: generateRandomCode(),
        apiKey,
      }]
    })
    closeChat('', sessionId);
    setInput('');
    setFileList([]);
    setConversationId('');
    setConversationSessionId(generateRandomNumber(10));
    setApiKey(getAppKey(appKeys, baseAppKeyName));
    setApiKeyName(baseAppKeyName);
  };
  
  const handleSendMessage = async () => {
    if (input.trim()) {
      const chatId = generateRandomCode(50);

      const newInputMessage: Message = {
        messageType: 'question',
        cardType: 'text',
        content: input,
        id: generateRandomCode(),
        apiKey,
      };

      // setState后立刻拿state无法获取最新值
      const { isAllowed, errorMsg } = await checkChatRateLimit(chatId, sessionId, hasAppointedLlm, hasConnectNet);
      if (!isAllowed) {
        setMessages((prevMessages) => [
          ...prevMessages,
          newInputMessage,
          { cardType: 'text', messageType: 'answer', content: errorMsg, id: generateRandomCode(), apiKey },
        ]);
        return;
      }

      let newFileMessage: Message | null = null;
      if (fileList.length > 0) {
        newFileMessage = {
          messageType: 'question',
          cardType: 'file',
          content: fileList,
          id: generateRandomCode(),
          apiKey,
        };
      }
      const newAnswerMessage: Message = {
        messageType: 'answer',
        cardType: 'text',
        content: '正在生成中',
        status: 'waiting',
        id: generateRandomCode(),
        apiKey,
        apiKeyName,
        chatId, // 用于清除会话
      };
      const newMessages = [...messages, ...(newFileMessage ? [newFileMessage] : []), newInputMessage, newAnswerMessage];
      setFileList([]);
      setMessages(newMessages);
      const level = 3;
      getRealtimeMessage(input, newMessages, newAnswerMessage.id, apiKey, apiKeyName, conversationId, level, chatId);
    }
  };

  const chatBoxRef = useRef<HTMLDivElement | null>(null);

  const handleMessageError = (error: any, messageId: string) => {
    setMessages((prevMessages) => {
      const newMessages = prevMessages.map(item => {
        if (item.id === messageId) {
          return { ...item, cardType: 'text',  status: 'error', content: MESSAGE.SYSTEM_ERROR } as Message;
        }
        return item;
      });
      return newMessages
    })
  }


  function extractCodeBlocks(text) {
    // 正则表达式匹配代码块中的内容，支持多种语言
    const regex = /```(jsx|tsx|js|python|javascript|html|css)([\s\S]*?)```/g;

    let match;
    const codeBlocks = [];

    // 遍历所有匹配项
    while ((match = regex.exec(text)) !== null) {
        codeBlocks.push(match[2].trim()); // 只捕获代码块中的内容
    }

    return codeBlocks[0]; // 返回所有匹配的代码块内容
}

  const getIframeCode = async (message: string) => {
    const myHeaders = new Headers();
    myHeaders.append('Content-Type', 'application/json');
    myHeaders.append('Cookie', 'acw_tc=0b32822617412219704746844e1a9326edb6ce51decbd617ba7a658f3b7b33');
    const raw = JSON.stringify({
      compoent: message
    });
    const requestOptions = {
      method: 'POST',
      headers: myHeaders,
      body: raw,
      redirect: 'follow'
    };
    const response = await fetch(
      '/api-fe-server/api/component2html',
      requestOptions
    );
    const data = await response.text();
    return data;
  }


  
  const getRealtimeMessage = async (queryInput: string, messages: Message[], messageID: string, appKey: string, appKeyName: string, currentConversionId: string , level: number, chatId?: string) => {
    try {
      const onError = (error: any, messageId: string) => {
        if (chatId) {
          closeChat(chatId, sessionId);
        }
        handleMessageError(error, messageId);
      }
      // 注册用户
      // token在ai agent传递的过程中会被篡改，采取另一种鉴权方式，即调用ai接口前先注册用户。具体可询问包明天
      const response = await fetch(
        '/api-opc/ai/registerByUsername',
        {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${getCookie()}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            sessionId: conversationSessionId,
            userName: username || '',
            token: `bearer ${getCookie()}`,
          }),
        }
      );

      const res = await response.json();
      if(res.code !== 200) {
        onError(res.message || '注册用户失败，请重试', messageID);
        throw new Error('Failed to register');
      }

      let query = queryInput;
      if (fileList && fileList.length > 0) {
        const urls = fileList.map((file) => file.url);
        urls.push(query);
        query = urls.join('\n');
      }
      const queryData = {
        query,
        response_mode: 'streaming',
        user: username || '',
        conversation_id: currentConversionId || '',
        IsOnlineSearch: hasConnectNet ? 1 : 0,
        IsDeepThinking:  hasAppointedLlm ? 1 : 0,
      }
      sendMessages({
        apiKey: appKey,
        username: username || '',
        queryData,
        messageId: messageID,
        conversationSessionId,
        onMessage: (data: string, messageId: string, conversationId: string) => {
          if (data != null) {
            const newMessages = messages.map(item => {
              if (item.id === messageId) {
                const newStatus = data ? 'pending' : 'waiting';
                const newContent = data || '正在生成中';
                return { ...item, cardType: 'text', status: newStatus, content: newContent } as Message;
              }
              return item;
            });
            console.log('newMessages', newMessages)
            setMessages([...newMessages]);
            setConversationId(conversationId);
          }
        },
        onError,
        onClose: async (message, messageId: string, conversationId: string, cardType?: string) => {
          let cid = conversationId;
          let extra = '';

          let _appKey = apiKey;
          let _appKeyName = appKeyName;
          level--;
          setConversationId(conversationId);
          console.log('message: ', message);
          console.log('-----level-----', level);
          let _message = message;
          if (typeof message === 'string') {
            extra = extractAllContents(message) || '';
            if (extra) {
              _appKey = getAppKey(appKeys, extra);
              console.log('appKey：', extra, _appKey);
              if (_appKey) {
                setApiKey(_appKey);
                setApiKeyName(extra);
                _appKeyName = extra;
                cid = '';
                setConversationId('');
              } else {
                _message = MESSAGE.AGENT_ERROR;
              }
            }
          }
          // 循环调用三次后还是返回agent路由，直接返回通用报错
          if (level === 0 && extra) {
            _message = MESSAGE.AGENT_ERROR;
          }
          // 收到agent路由后，需要调用下一个agent
          const needNextMessage = extra && _appKey && level > 0;
          const newMessages = await Promise.all(
            messages.map(async item => {
              if (item.id === messageId) {
                if (cardType === 'error') {
                  return { ...item, cardType: 'error', content: _message, status: 'success', apiKey, conversationId: cid } as Message;
                }
                const codeBlocks = extractCodeBlocks(message);
                if (codeBlocks) {
                  const iframeCode = await getIframeCode(codeBlocks);
                  return { ...item, cardType: 'iframe', content: iframeCode, status: 'success', apiKey, apiKeyName: _appKeyName, conversationId: cid } as Message;
                }
                if (typeof message === 'string') {
                  return { ...item, content: _message, status: needNextMessage ? 'pending' : 'success', apiKey, apiKeyName: _appKeyName, conversationId: cid } as Message;
                }
                if (message && message.products) {
                  return { ...item, cardType: 'product', content: _message, status: 'success', apiKey, apiKeyName: _appKeyName, conversationId: cid } as Message;
                }
              }
              return item;
            })
          );
          setMessages([...newMessages])
          console.log('extra', extra);
          if (needNextMessage) {
            getRealtimeMessage(input, newMessages, messageId, _appKey, _appKeyName, '', level);
          }
          // 每次会话结束后需要清除会话
          if (chatId) {
            closeChat(chatId, sessionId);
          }
        },
      });
    } catch (error) {
      console.error('Failed to send message:', error);
    } finally {
      // 清空输入框
      setInput('');
    }
  };

  const sendIframeMessage = async (source: string, message: string, params: any) => {
    console.log('准备开始发消息了，', message, params);
    const _params = params;
    // selectFlow选择流程这一步骤的入参保存至flowData中，第二次提交editflow时，将flowData中的数据和message合并
    if (source === 'selectFlow' && params) {
      setFlowData(params);
    }
    const chatId = generateRandomCode(50);

    const { isAllowed, errorMsg } = await checkChatRateLimit(chatId, sessionId, hasAppointedLlm, hasConnectNet);
    if (!isAllowed) {
      setMessages((prevMessages) => [
        ...prevMessages,
        // newInputMessage,
        { cardType: 'text', messageType: 'answer', content: errorMsg, id: generateRandomCode(), apiKey },
      ]);
      return;
    }
    const newAnswerMessage: Message = {
      messageType: 'answer',
      cardType: 'text',
      content: '正在生成中',
      status: 'waiting',
      id: generateRandomCode(),
      apiKey,
      apiKeyName,
      chatId, // 用于清除会话
    };
    console.log('newAnswerMessage', newAnswerMessage);
    const newMessages = [...messages, newAnswerMessage];
    setMessages(newMessages);
    const level = 3;
    if (source === 'selectFlow') {
      getRealtimeMessage(message, newMessages, newAnswerMessage.id, apiKey, apiKeyName, conversationId, level, chatId);
    } else if (source === 'editFlow') {
      const dataStr = JSON.stringify({...flowData, runParams: JSON.stringify(_params)});
      const input = `${message}\n${dataStr}`;
      console.log('第二次提交', input);
      getRealtimeMessage(input, newMessages, newAnswerMessage.id, apiKey, apiKeyName, conversationId, level, chatId);
    }
  }

  const scrollToBottom = () => {
    if (chatBoxRef.current) {
      chatBoxRef.current.scrollTop = chatBoxRef.current.scrollHeight;
    }
  };

  const handleUploadFile = async (options: any) => {
    try {
      const { onSuccess, onError, file } = options;
      setLoading(true);
      const response = await upload('ai-helper', file);
      if (response && response.url) {
        onSuccess(response);
      } else {
        onError('上传失败');
      }
    } catch (error) {
      console.error('Failed to upload file:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpload: UploadProps['onChange'] = async (info) => {
    try {
      setLoading(true);
      if (info.file.status === 'done') {
        const newFile = {
          ...info.file,
          url: info.file.response.data,
        };
        setFileList([...fileList, newFile]);
        setLoading(false);
      }
    } catch (error) {
      console.error('Failed to upload file:', error);
    }
  };

  const afterRemove = (file: UploadFile, event: React.MouseEvent) => {
    event.stopPropagation();
    setFileList(fileList.filter((f) => f.uid !== file.uid));
  };

  const handleItemClick = (item: any) => {
    console.log('item', item, appKeys);
    setHasAppointedLlm(false)
    setHasConnectNet(false)
    setMessages([...messages, { cardType: 'text', messageType: 'answer', content: item.description, id: generateRandomCode(), apiKey, apiKeyName: item.name }]);
    console.log('messages', messages)
    if (appKeys?.[item.name]) {
      const appKey = getAppKey(appKeys, item.name);
      if (appKey) {
        setApiKey(appKey);
        setApiKeyName(item.name);
        setConversationId('');
      }
    }
  };
  const handleMore = (productName: string) => {
    const questions = messages.filter((item) => item.messageType === 'question');
    const lastQuestion = productName || questions[questions.length - 1]?.content || '';
    window.open(`https://${isTestEnv() ? 'webuat' : 'www'}.zkh.com/search.html?keywords=${lastQuestion}&hasLinkWord=1`)
  }
  const handleCopy = (products: any[]) => {
    console.log('handleCopy', products)
    try {
      const json = JSON.stringify(products);
      navigator.clipboard.writeText(json);
      messageApi.open({
        type: 'success',
        content: '复制成功',
      });
    } catch (error) {
      console.log(error)
    }
  }
  const productTemp = (message: Message) => (
    <div style={{backgroundColor: '#fff', borderRadius: '20px', padding: '24px 20px'}}>
      <div className='mb-[10px]'>{message.content.answer.split('\n')[0]}</div>
      {message.content?.products?.length > 0 && message.content.products.map((item) => (
        <ProductCard item={item} /> 
      ))}
      <div className="flex justify-between items-center">
        <div onClick={() => handleMore(message.content.query)} className='text-[#495CE6] text-[12px] cursor-pointer'>查看更多SKU</div>
        <div>
          <i onClick={() => handleCopy(message.content.products)} className='iconfont icon-fuzhi mr-[10px] text-[16px] text-[#495CE6] cursor-pointer' />
          {/* <i className='iconfont icon-shuaxin-2 mr-[10px] text-[16px] text-[#495CE6]' />
          <i className='iconfont icon-dianzan1 mr-[10px] text-[16px] text-[#495CE6]' />
          <i className='iconfont icon-dianzan4 text-[16px] text-[#495CE6]' /> */}
        </div>
      </div>
    </div>
  )

  const handleStop = () => {
    abort();
    setMessages((prevMessages) => {
      const newMessages = prevMessages.map(item => {
        if (item.status === 'pending') {
          if (item.chatId) {
            closeChat(item.chatId, sessionId);
          }
          return { ...item, status: 'success' } as Message;
        }
        return item;
      });
      return newMessages
    })
  }
  useEffect(() => {
    scrollToBottom();
  }, [messages]);
  // 深度思考
  useEffect(() => {
    if (hasAppointedLlm) {
      const appKeyName = DEEPSEEK_APP_KEY_NAME;
      const appKey = getAppKey(appKeys, appKeyName);
      if (appKey) {
        setApiKey(appKey);
        setApiKeyName(appKeyName);
        setConversationId('');
      }
    } else {
      setApiKey(getAppKey(appKeys, baseAppKeyName));
      setApiKeyName(baseAppKeyName);
      setConversationId('');
    }
  }, [hasAppointedLlm]);

  const renderProducts = (message: Message) => {
    if (message.content.products.length > 0) {
      return <MessageProduct key={message.id} text={''} renderProducts={() => productTemp(message)} type={message.messageType} apiKeyName={message.apiKeyName} />
    } else {
      return <MessageText text={message.content.answer} type={message.messageType} status={message.status} />
    }
  }

  return (
    <div className="flex flex-col overflow-y-auto" style={{ height: 'calc(100vh - 98px)'}}>
      {contextHolder}
      <div ref={chatBoxRef} className="min-h-[500px] overflow-y-auto flex-1 py-2">
        <Flex vertical gap="middle">
          {messages.map((message) => {
            if (message.cardType === 'help') {
              return <HelpCard key={message.id} content={message.content as string} onItemClick={handleItemClick} />
            } else if (message.cardType === 'product') {
              return renderProducts(message)
            } else if (message.cardType === 'file') {
              return <FileCard key={message.id} fileList={message.content as UploadFile[]} type={message.messageType} />
            } else if (message.cardType === 'error') {
              return <ErrorCard msg={message.content as string}/>
            } else if (message.cardType === 'iframe') {
              return <RPAIframeCard key={message.id} text={message.content as string} apiKeyName={message.apiKeyName} type={message.messageType} sendIframeMessage={sendIframeMessage} />
            } else {
              return <MessageText key={message.id} text={message.content as string} status={message.status} apiKeyName={message.apiKeyName} type={message.messageType} />
            }
          })}
        </Flex>
      </div>
      {messages.some(item => item.status === 'pending') && <div className="flex justify-center"><Button type='primary' onClick={handleStop}>停止</Button></div>}
      <SenderEditor
        inputText={input}
        setInputText={setInput}
        onSend={handleSendMessage}
        onItemClick={handleItemClick}
        loading={loading}
        setLoading={setLoading}
        handleUploadFile={handleUploadFile}
        handleUpload={handleUpload}
        fileList={fileList}
        afterRemove={afterRemove}
        isMessagePending={isMessagePending}
      >
        <div style={{position:'absolute',left:'12px',bottom:'16px'}} className={`flex gap-[10px] ${isMessagePending() ? 'pointer-events-none' : 'cursor-pointer'}`}>
          <div id="ai_helper_deep_thinking_button" onClick={() => loading ? null : setHasAppointedLlm(!hasAppointedLlm)} style={{ backgroundColor: hasAppointedLlm ? '#E0EDFF' : '#F5F5F9' }} className='w-[106px] h-[26px] bg-[#E0EDFF] rounded-[13px] flex items-center justify-center'>
            <span style={{ color: hasAppointedLlm ? '#5198FF' : '#333' }} className='iconfont icon-shendusikao text-[14px] mr-[2px]'></span>
            <span style={{ color: hasAppointedLlm ? '#5198FF' : '#333' }} className='text-[12px] cursor-pointer'>深度思考(R1)</span>
          </div>
          <div id="ai_helper_connect_net_button" onClick={() => setHasConnectNet(!hasConnectNet)} style={{ backgroundColor: hasConnectNet ? '#E0EDFF' : '#F5F5F9' }} className='w-[86px] h-[26px] bg-[#F5F5F9] rounded-[13px] flex items-center justify-center'>
          <span style={{ color: hasConnectNet ? '#5198FF' : '#333' }} className='iconfont icon-hulianwang text-[14px] mr-[2px]'></span>
            <span style={{ color: hasConnectNet ? '#5198FF' : '#333' }} className='text-[12px] cursor-pointer'>联网搜索</span>
          </div>
        </div>
      </SenderEditor>
      <div className={`flex justify-between my-[20px] ${isMessagePending() ? 'pointer-events-none' : 'cursor-pointer'}`}>
        <div id="ai_helper_new_chat_button" className='flex items-center justify-center w-[70px] h-[26px] bg-[#E4E3F6] rounded-[13px]'>
          <span className="icon iconfont text-[#495CE6] text-[16px]">&#xe647;</span>
          <span className="ml-[6px] cursor-pointer text-[#495CE6] text-[12px]" onClick={() => newChat()}>新对话</span>
        </div>
        <div id="ai_helper_clear_chat_button" className="flex justify-end text-[#999] text-[12px] items-center">
          <span className="icon iconfont">&#xe624;</span>
          <span className="ml-[6px] cursor-pointer" onClick={() => clearChat()}>清空对话</span>
        </div>
      </div>
    </div>
  );
};

export default Home;

