import fs from 'node:fs';
import path, { parse } from 'node:path';

const env = 'pro';

const idConfig = {
  uat: {
    欢迎卡片: 233,
    欢迎卡片_gray: 237,
    欢迎卡片_OA: 243,
    appkey: 234,
    appkey_gray: 240,
    appkey_OA: 248,
  },
  pro: {
    欢迎卡片: 3002,
    欢迎卡片_gray: 3050,
    欢迎卡片_OA: 3108,
    appkey: 3007,
    appkey_gray: 3051,
    appkey_OA: 3105,
  }
}

// access token
const token =
'bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************.5G2cy4xHETqrSexbsGwPRkVkbRO65zIYwxUuiYNGaVw'
// 配置地址,注意区别开发环境
const url = `https://zkh-form-center-${env}.zkh360.com/front-end/display/template/edit`;

const currentDir = 'AI行家助手_BOSS';
try {
  const currentDirPath = path.resolve();
  const dir = path.join(currentDirPath, `../templateData/${currentDir}`);
  fs.readdirSync(dir, { withFileTypes: true }).forEach(function (dirent) {
    const filePath = path.join(dir, dirent.name);
    if (dirent.isFile() && dirent.name.indexOf('.json') > -1) {
      let data = fs.readFileSync(filePath, 'utf8');
      if (data) {
        const parseData = JSON.parse(data);
        const templateInfo = JSON.stringify(parseData.templateInfo);
        data = JSON.stringify({
          ...parseData,
          appointmentKeyId: idConfig[env][dirent.name.split('.')[0]],
          templateInfo,
        });
        console.log(idConfig[env][dirent.name.split('.')[0]])
        fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `${token}`,
          },
          body: data,
        })
          .then((res) => res.json())
          .then((res) => {
            console.log(dirent.name, '---->', res);
          });
      }
    }
  });
} catch (err) {
  console.error(err);
}
