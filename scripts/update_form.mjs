import fs from 'node:fs';
import path, { parse } from 'node:path';

const env = 'pro';

const idConfig = {
  uat: {
    欢迎卡片: 233,
    appkey: 234
  },
  pro: {
    欢迎卡片: 3002,
    appkey: 3007
  }
}
// access token
const token =
'bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************.89w_IMgde_YJkCoUiFAl-yzrHq_-lLz3hBzltT7PU6s'
// 配置地址,注意区别开发环境
const url = `https://zkh-form-center-${env}.zkh360.com/front-end/display/template/edit`;

const currentDir = 'AI行家助手_BOSS';
try {
  const currentDirPath = path.resolve();
  const dir = path.join(currentDirPath, `../templateData/${currentDir}`);
  fs.readdirSync(dir, { withFileTypes: true }).forEach(function (dirent) {
    const filePath = path.join(dir, dirent.name);
    if (dirent.isFile() && dirent.name.indexOf('.json') > -1) {
      let data = fs.readFileSync(filePath, 'utf8');
      if (data) {
        const parseData = JSON.parse(data);
        const templateInfo = JSON.stringify(parseData.templateInfo);
        data = JSON.stringify({
          ...parseData,
          appointmentKeyId: idConfig[env][dirent.name.split('.')[0]],
          templateInfo,
        });
        console.log(idConfig[env][dirent.name.split('.')[0]])
        fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `${token}`,
          },
          body: data,
        })
          .then((res) => res.json())
          .then((res) => {
            console.log(dirent.name, '---->', res);
          });
      }
    }
  });
} catch (err) {
  console.error(err);
}
