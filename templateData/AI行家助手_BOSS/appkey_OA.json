{"configType": "AgentApiKeyConfig_OA", "customizedAlias": "智能体ApiKey配置_OA", "isDefault": "Y", "appointmentKeyId": "3007", "keyJson": {"formName": "智能体ApiKey配置_OA", "formType": "AgentApiKeyConfig_OA", "configType": "AgentApiKeyConfig_OA"}, "keyResolveRule": "configType-formName", "owner": "SYS", "templateInfo": {"通用查询": {"uat": "app-YSUKVeyuiZtbKyNbUvoj7GRC", "pro": "app-Aybp0VHSnkEvyz5Ji9I8UCIh"}, "OA智能助手": {"uat": "app-YSUKVeyuiZtbKyNbUvoj7GRC", "pro": "app-Aybp0VHSnkEvyz5Ji9I8UCIh"}}}