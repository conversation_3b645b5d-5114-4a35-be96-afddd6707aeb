{"configType": "AI_HELPER_BOSS_WELCOME_CARD_V2", "customizedAlias": "AI行家助手_BOSS欢迎卡片V2", "isDefault": "Y", "appointmentKeyId": "3002", "keyJson": {"formName": "AI行家助手_BOSS欢迎卡片V2", "formType": "AI_HELPER_BOSS_WELCOME_CARD_V2", "configType": "AI_HELPER_BOSS_WELCOME_CARD_V2"}, "keyResolveRule": "configType", "owner": "SYS", "templateInfo": {"greeting": "你好，欢迎使用最新版AI行家助手~", "description": "已经接入Deepseek在内的多个大模型能力，我可以帮你查信息、处理业务，以及任何问题，请让我成为你的私人工作助理吧", "helpTopics": [{"title": "我可以这样帮助你：", "items": [{"title": "找信息", "items": [{"name": "找人", "description": "我支持找产线、找销售、找客服、找仓库、找值班人员，请您输入查找的具体角色。"}, {"name": "找客户", "description": "我支持查找客户的基本信息，请输入客户名称，并输入“查客户”。"}, {"name": "查商品信息", "description": "请输入sku+城市，查找商品信息及相关资料；输入品牌名称+查授权，品牌授权文件等。"}]}, {"title": "业务查询", "items": [{"name": "找知识", "description": "我支持产线、人事、行政、IT的相关知识查询。"}]}, {"title": "特定域业务查询及处理", "items": [{"name": "AI搜索找货", "description": "请输入关键词、sku、或上传商品图片，自动找货等"}, {"name": "订单域查询及处理", "description": "我是您的专业AI订单操作助手，随时准备帮助您处理订单相关的事务。目前支持创建草稿单、查询单据相关信息、处理订单行库位修改等操作。请告诉我您的具体需求，我会立即调用相应的工具为您提供支持！"}, {"name": "财务域查询及处理", "description": "我是您的专业自动开票助手，我可以完成各类单据的自动开票，您可以输入 各类订单号执行自动创建对账单并自动提交开票。你可以这样对我说：将sap订单 1500021012 1010096815 合并开票。 将sap交货订单 8100093849 8100093849 分开开票。提交自动开票同时支持 开票类型、ESP参考凭证、备注 等信息自定义，如需要修改，请将对应字段告诉ai即可。例如：将交货单 8500006025 开票，开票类型：数电普票，ESP参考凭证号：137219469748，备注：开票测试"}, {"name": "采购域查询及处理", "description": "我是您的专业采购域业务处理助手，我可以完成各类采购单信息查询指引，请提供你的问题"}, {"name": "工单域查询及处理", "description": "我是你的工单域业务意图识别智能体，能够根据你的诉求执行工单域的相关操作"}]}, {"title": "其他", "items": [{"name": "查询个人信息", "description": "我支持员工考勤、假期、报销、津贴查询，请输入如 “查考勤” 。"}, {"name": "查找其他功能", "description": "我支持深度思考和联网搜索。"}]}]}], "tabNavigation": [{"name": "找人", "description": "我支持找产线、找销售、找客服、找仓库、找值班人员，请您输入查找的具体角色。"}, {"name": "找客户", "description": "我支持查找客户的基本信息，请输入客户名称，并输入“查客户”。"}, {"name": "AI搜索找货", "description": "请输入关键词、sku、或上传商品图片，自动找货等。"}, {"name": "订单域查询及处理", "description": "我是您的专业AI订单操作助手，随时准备帮助您处理订单相关的事务。目前支持创建草稿单、查询单据相关信息、处理订单行库位修改等操作。请告诉我您的具体需求，我会立即调用相应的工具为您提供支持！"}, {"name": "财务域查询及处理", "description": "我是您的专业自动开票助手，我可以完成各类单据的自动开票，您可以输入 各类订单号执行自动创建对账单并自动提交开票。你可以这样对我说：将sap订单 1500021012 1010096815 合并开票。 将sap交货订单 8100093849 8100093849 分开开票。提交自动开票同时支持 开票类型、ESP参考凭证、备注 等信息自定义，如需要修改，请将对应字段告诉ai即可。例如：将交货单 8500006025 开票，开票类型：数电普票，ESP参考凭证号：137219469748，备注：开票测试"}, {"name": "采购域查询及处理", "description": "我是您的专业采购域业务处理助手，我可以完成各类采购单信息查询指引，请提供你的问题"}, {"name": "工单域查询及处理", "description": "我是你的工单域业务意图识别智能体，能够根据你的诉求执行工单域的相关操作"}, {"name": "查商品信息", "description": "请输入sku+城市，查找商品信息及相关资料；输入品牌名称+查授权，品牌授权文件等。"}, {"name": "找知识", "description": "我支持产线、人事、行政、IT的相关知识查询。"}, {"name": "查询个人信息", "description": "我支持员工考勤、假期、报销、津贴查询，请输入如 “查考勤”。"}, {"name": "查找其他功能", "description": "我支持深度思考和联网搜索。"}, {"name": "OA智能助手", "description": "我是OA智能助手。"}]}}