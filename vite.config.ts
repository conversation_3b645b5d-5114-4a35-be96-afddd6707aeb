import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'
import { fileURLToPath, URL } from 'url';

const isProd = process.env.NODE_ENV === 'production';

// const target = 'http://localhost:3000'
const target = 'https://boss-uat.zkh360.com'
// https://vite.dev/config/
export default defineConfig({
  base: isProd ? 'https://files.zkh360.com/assets/ai-helper/' : '/ai-helper/',
  plugins: [react(), tailwindcss()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  server: {
    port: 5173,
    allowedHosts: ['local.zkh360.com'],
    proxy: {
      '/api-agent': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/api-oss': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/ali-upload': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/internal-api': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/api-form-center': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/api-opc': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/api-fe-server': {
        target,
        changeOrigin: true,
        secure: false,
      },
      '/api-rpa': {
        target: 'https://zkh-rpa-service-uat.zkh360.com/',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api-rpa/, ''),
      },
    },
  }
})
