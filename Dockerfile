
FROM newhub.zkh360.com/fe/nodejs:22.11.0-alpine3.20-pnpm9.12.3 AS base

FROM base as builder

WORKDIR /app
COPY . ./
RUN pnpm config set registry "https://registry.npmmirror.com/" && \
  pnpm install && \
  pnpm run build

FROM newhub.zkh360.com/fe/ossutil-boss:1.7.19 AS uploader
WORKDIR /usr/local/bin
COPY --from=builder /app ./app
RUN ./ossutil cp -r app/dist/ oss://zkh360-boss/assets/ai-helper -e oss-cn-beijing.aliyuncs.com

FROM newhub.zkh360.com/fe/nginx:1.23.1-alpine
ARG ENV
ENV ENV=${ENV}
COPY --from=uploader /usr/local/bin/app/dist/index.html /opt/web/
COPY --from=uploader /usr/local/bin/app/web.conf /etc/nginx/conf.d/
