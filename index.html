<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="https://files.zkh360.com/assets/qtsai/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>震坤行 - AI行家助手</title>
    <script src="https://files.zkh360.com/ai-helper/iconfont/iconfont.js"></script>
    <script charset="UTF-8" src="https://files.zkh360.com/assets/sensorsdata.min.js"></script>
    <script>
      const isPro = () => !/uat|test|local/.test(location.href);
      const initSensor = () => {
        var sensors = window['sensorsDataAnalytic201505'];
        sensors.init({
          server_url: isPro() ? 'https://tracking.zkh.com/sa?project=production' : 'https://tracking.zkh.com/sa?project=abroad',
          show_log: false,
          is_track_single_page: true,
          use_client_time: true,
          send_type: 'beacon',
          heatmap: {
            clickmap: 'default',
            scroll_notice_map: 'default'
          },
        });
        sensors.quick('autoTrack');
      }
      initSensor();
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
